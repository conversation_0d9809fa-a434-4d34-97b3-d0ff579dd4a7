<?php

namespace app\common\command;

use app\common\model\auth\Admin;
use app\common\model\user\User;
use app\common\service\ConfigService;
use app\common\service\FileService;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Config;
use think\facade\Log;

class AutoTaskForAgents extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('agents:task')
            ->setDescription('每分钟添加随机个数的推广用户');
    }

    protected function execute(Input $input, Output $output)
    {
        // 记录任务开始时间
        $startTime = microtime(true);
        $output->writeln('[' . date('Y-m-d H:i:s') . '] 开始执行用户添加任务...');

        // 定义推广用户性别数组  1-男；2-女
        $sexArr = [1, 2];

        // 获取后台数据中心配置信息
        $getAdminConfig = ConfigService::get('agent_config', 'agent_config', '');

        try {
            //获取所有推广人
            $adminList = Admin::where('root',0)->select()->toArray();
            $defaultAvatar = config('project.default_image.admin_avatar');
            $passwordSalt = Config::get('project.unique_identification');
            $password = create_password('123456', $passwordSalt);
            foreach ($adminList as $admin) {
                //获取配置的增加人数范围
                $userCount = mt_rand($getAdminConfig['minAgentNums'], $getAdminConfig['maxAgentNums']);
                $userData = [];
                for ($i = 0; $i < $userCount; $i++) {
                    $userName = 'user_' . time() . '_' . mt_rand(1000, 9999);
                    $userData[] = [
                        'admin_id' => $admin['id'],
                        'sn' => User::createUserSn(),
                        'account' => $userName,
                        'password' => $password,
                        'avatar' => $defaultAvatar,
                        'nickname' => '用户' . mt_rand(1000, 9999),
                        'sex' => $sexArr[array_rand($sexArr)],
                        'create_time' => date('Y-m-d H:i:s',time()),
                    ];
                }
                $userModel = new User();
                $userModel->saveAll($userData);
            }

            // 记录执行结果
            $execTime = round(microtime(true) - $startTime, 3);
            $output->writeln("任务完成! 耗时: {$execTime}秒");

            // 写入日志
            Log::info("任务成功耗时 {$execTime}秒");

            return 0;

        }catch (\Exception $e){
            $output->writeln('任务执行出错: ' . $e->getMessage());
            Log::error('任务错误: ' . $e->getMessage());
            return 1;
        }
    }
}
