<?php

namespace app\common\command;

use app\common\model\auth\Admin;
use app\common\model\consumes\Consumes;
use app\common\model\income\AgentIncome;
use app\common\model\user\User;
use app\common\service\ConfigService;
use app\common\service\FileService;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Config;
use think\facade\Log;

class AutoTaskForVideos extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('videos:task')
            ->setDescription('每分钟添加随机个数的推广用户');
    }

    protected function execute(Input $input, Output $output)
    {
        // 记录任务开始时间
        $startTime = microtime(true);
        $output->writeln('[' . date('Y-m-d H:i:s') . '] 开始执行用户视频任务...');

        // 定义视频数组
        $videoArr = config('consume.video');
//        print_r($giftArr);exit;
//        {"convertPercent":"10","minAgentNums":"1","maxAgentNums":"5","rechargeUserNums":"20","minRechargeNums":"1","maxRechargeNums":"3","minConsumesNums":"1","maxConsumesNums":"3","rechargePercent":"50","consumeGiftUserNums":"10","consumeVideoUserNums":"10","userGiftPercent":"20","userVideoPercent":"20","agentGiftPercent":"20","agentVideoPercent":"20","consumeGiftsUserNums":"10"}
        // 获取后台数据中心配置信息
        $getAdminConfig = ConfigService::get('agent_config', 'agent_config', '');
        try {
            // 从数据库获取随机取N条男性用户数据
            // 获取所有男性用户ID  且金币余额不能低于视频数组中价格最低的，保证能赠送成功
            $manIds = User::where('sex',1)->where('balance','>=',self::getCheapestVideo($videoArr))->column('id');
            if (count($manIds) <= $getAdminConfig['consumeVideoUserNums']) {
                // 如果用户数量不足数据中心配置的配置数量，则取全部
                $manUserList = User::where('sex',1)->select()->toArray();

            }else {
                // 随机打乱并取数据中心配置的配置数量
                shuffle($manIds);
                $randomManIds = array_slice($manIds, 0, $getAdminConfig['consumeVideoUserNums']);
                $manUserList = User::where('sex',1)->where('id', 'in', $randomManIds)->select()->toArray();
            }

            // 简单匹配方式，一对一视频---从数据库获取与男性用户数量一样数量的女性用户数据
            // 获取所有女性用户ID
            $womanIds = User::where('sex',2)->column('id');
            // 随机打乱并取数据中心配置的配置数量
            shuffle($womanIds);

            $randomWomanIds = array_slice($womanIds, 0, count($manUserList));
            $womanUserList = User::where('sex',2)->where('id', 'in', $randomWomanIds)->select()->toArray();

            //初始化推广员收益数组
            $agentIncome = [];

            //初始化用户累计余额变动数组
            $userMoney = [];

            //初始化用户累计收益变动数组
            $userIncome = [];
            //给主播赠送礼物，推广员拿主播收益的收益比例
            $agentVideoPercent = bcdiv($getAdminConfig['agentVideoPercent'], 100, 2);
            foreach ($manUserList as $key => $manUser) {
                // 为用户生成随机条数、随机金额的送礼物记录
                // 随机条数
                $consumeCount = mt_rand($getAdminConfig['minConsumesNums'], $getAdminConfig['maxConsumesNums']);
                $consume = [];
                $incomes = [];
                for ($j = 0; $j < $consumeCount; $j++) {
                    // 根据用户余额  获取能赠送的礼物的价格区间
                    $videoCoinRange = self::getAffordableVideoRange($videoArr,$manUser['balance']);
                    //若用户余额不够进行多次送礼，则直接跳出循环
                    if(empty($videoCoinRange)) {
                        break;
                    }

                    //本次视频花费的金币  ===  本次主播收益金币
                    $videoCoin = $videoCoinRange[array_rand($videoCoinRange)];

                    $consume[] = [
                        'user_id' => $manUser['id'],
                        'to_user_id' => $womanUserList[$key]['id'],
                        'type' => 2,
                        'amount' => $videoCoin,
                        'income' => $videoCoin,
                        'create_time' =>date('Y-m-d H:i:s',time()),
                    ];

                    //用户余额扣减数组
                    if (isset($userMoney[$manUser['id']])) {
                        $userMoney[$manUser['id']] += $videoCoin;
                    }else{
                        $userMoney[$manUser['id']] = $videoCoin;
                    }

                    //主播收益增加数组
                    if (isset($userIncome[$womanUserList[$key]['id']])) {
                        $userIncome[$womanUserList[$key]['id']] += $videoCoin;
                    }else{
                        $userIncome[$womanUserList[$key]['id']] = $videoCoin;
                    }

                    //推广员返佣记录数组
                    $incomes[] = [
                        'admin_id' => $womanUserList[$key]['admin_id'],            //推广员ID
                        'user_id' => $womanUserList[$key]['id'],                   //推广用户ID
                        'type' => 2,                                 //来自渠道类型  1-充值 2-消费
                        'amount' => bcmul($videoCoin,$agentVideoPercent,2),            //收益金额
                        'create_time' => date('Y-m-d H:i:s',time()),
                    ];

                    //推广员累计返佣金额数组
                    if (isset($agentIncome[$womanUserList[$key]['admin_id']])) {
                        $agentIncome[$womanUserList[$key]['admin_id']] += bcmul($videoCoin,$agentVideoPercent,2);
                    }else{
                        $agentIncome[$womanUserList[$key]['admin_id']] = bcmul($videoCoin,$agentVideoPercent,2);
                    }
                }

                $consumeModel = new Consumes();
                $consumeModel->saveAll($consume);

                $incomeModel = new AgentIncome();
                $incomeModel->saveAll($incomes);
            }

            foreach ($userMoney as $userId => $amount) {
                $user = User::find($userId);
                $user->balance = $user['balance'] - $amount;
                $user->save();
            }

            foreach ($userIncome as $k => $v) {
                $user = User::find($k);
                $user->income = $user['income'] + $v;
                $user->save();
            }

            foreach ($agentIncome as $adminId => $amount) {
                $admin = Admin::find($adminId);
                $admin->total_coin = $admin['total_coin'] + $amount;
                $admin->save();
            }

            // 记录执行结果
            $execTime = round(microtime(true) - $startTime, 3);
            $output->writeln("任务完成! 耗时: {$execTime}秒");

            // 写入日志
            Log::info("任务成功耗时 {$execTime}秒");

            return 0;

        }catch (\Exception $e){
            $output->writeln('任务执行出错: ' . $e->getMessage());
            Log::error('任务错误: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * @notes 获取视频价格数组总价格最低的单价
     * <AUTHOR>
     * @date 2021/12/29 14:30
     */
    public static function getCheapestVideo($data) {
        if (empty($data)) return null;

        // 找到最低价格
        $minPrice = min($data);

        // 返回第一个匹配最低价格的礼物
        return $data[array_search($minPrice, $data)];
    }

    /**
     * 判断用户余额能与主播视频价格区间
     * @param array $videoCoin 视频价格数组（无需排序，函数内会处理）
     * @param float $balance 用户余额
     * @return array 包含区间信息的关联数组
     */
    public static function getAffordableVideoRange($videoCoin, $balance) {

        $count = count($videoCoin);

        // 余额不足购买最便宜礼物
        if ($balance < $videoCoin[0]) {
            return [];
        }

        // 余额可购买所有礼物
        if ($balance >= $videoCoin[$count - 1]) {
            return $videoCoin;
        }

        // 二分查找确定最大可购买价格
        $low = 0;
        $high = $count - 1;
        $maxIndex = 0;

        while ($low <= $high) {
            $mid = floor(($low + $high) / 2);

            if ($videoCoin[$mid] <= $balance) {
                $maxIndex = $mid;
                $low = $mid + 1;
            } else {
                $high = $mid - 1;
            }
        }

        // 返回区间信息
        return array_slice($videoCoin, 0, $maxIndex + 1);
    }
}
