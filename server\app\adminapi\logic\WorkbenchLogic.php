<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\logic;


use app\common\logic\BaseLogic;
use app\common\model\auth\Admin;
use app\common\model\consumes\Consumes;
use app\common\model\income\AgentIncome;
use app\common\model\recharges\Recharges;
use app\common\model\user\User;
use app\common\service\ConfigService;
use app\common\service\FileService;
use Carbon\Carbon;
use think\Db;


/**
 * 工作台
 * Class WorkbenchLogic
 * @package app\adminapi\logic
 */
class WorkbenchLogic extends BaseLogic
{
    /**
     * @notes 工作套
     * @param $adminInfo
     * @return array
     * <AUTHOR>
     * @date 2021/12/29 15:58
     */
    public static function index($adminId)
    {
        $date = Carbon::now()->toDateTimeString();
        $dates = [];
        $today = Carbon::today();
        // 获取从今天开始往前推7天的日期（不包括今天）
        for ($i = 1; $i <= 7; $i++) {
            $dates[] = $today->copy()->subDays($i)->format('Y-m-d');
        }
        $week = array_reverse($dates);
        return [
            // 版本信息
            'updateTime' => $date,
            // 今日收益
            'todayIncome' => self::todayIncome($adminId,$date),
            // 今日充值
            'todayRechargeAmounts' => self::todayRechargeAmounts($adminId,$date),
            // 今日邀请人数
            'todayInvite' => self::todayInvite($adminId,$date),
            // 可提现金额
            'balance' => self::balance($adminId),
            // 今日消费
            'consume' => self::consume($adminId,$date),
            // 一周内收益趋势
            'chartDataByIncomeLine' => self::chartDataByIncomeLine($adminId,$week),
            // 一周内用户充值趋势
//            'chartDataByRechargeLine' => self::chartDataByRechargeLine(),
//            // 一周内邀请人数趋势
//            'chartDataColumn' => self::chartDataColumn(),
//            // 邀请用户性别占比
//            'chartDataBySexNumPercent' => self::chartDataBySexNumPercent(),
//            // 邀请用户充值占比
//            'chartDataRechargePercent' => self::chartDataRechargePercent(),
//            // 佣金收益占比
//            'chartDataIncomePercent' => self::chartDataIncomePercent(),
//            // 充值金额排名前十用户
//            'chartDataRechargeTopBar' => self::chartDataRechargeTopBar()
        ];
    }


    /**
     * @notes 今日收益
     * @return array[]
     * <AUTHOR>
     * @date 2021/12/29 16:40
     */
    public static function todayIncome($adminId,$date)
    {
        $start = Carbon::parse($date)->startOfDay()->toDateTimeString();
        $amount  = AgentIncome::where('admin_id',$adminId)->whereBetween('create_time',[$start,$date])->sum('amount');
        return $amount ?: 0;
    }


    /**
     * @notes 今日充值
     * @return array
     * <AUTHOR>
     * @date 2021/12/29 16:08
     */
    public static function todayRechargeAmounts($adminId,$date)
    {
        $start = Carbon::parse($date)->startOfDay()->toDateTimeString();
        $amount = Recharges::alias('r')
            ->join('user u', 'u.id = r.user_id')
            ->where('u.admin_id', $adminId)
            ->whereBetween('r.create_time', [$start, $date])
            ->sum('r.amount');
        return $amount ?: 0;
    }


    /**
     * @notes 今日邀请人数
     * @return int[]
     * <AUTHOR>
     * @date 2021/12/29 16:15
     */
    public static function todayInvite($adminId,$date)
    {
        $start = Carbon::parse($date)->startOfDay()->toDateTimeString();
        $count = User::where('admin_id', $adminId)->whereBetween('create_time', [$start, $date])->count();
        return $count ?: 0;
    }


    /**
     * @notes 可提现金额
     * @return array
     * <AUTHOR>
     * @date 2021/12/29 16:57
     */
    public static function balance($adminId)
    {
        $admin = Admin::find($adminId);
        return $admin->total_coin;
    }

    /**
     * @notes 今日消费
     * @return array
     * <AUTHOR>
     * @date 2021/12/29 16:57
     */
    public static function consume($adminId,$date)
    {
        $start = Carbon::parse($date)->startOfDay()->toDateTimeString();
        $amount = Consumes::alias('c')
            ->join('user u', 'u.id = c.user_id')
            ->where('u.admin_id', $adminId)
            ->whereBetween('c.create_time', [$start, $date])
            ->sum('c.amount');
        return $amount ?: 0;
    }


    /**
     * @notes 一周内收益
     * @return array[]
     * <AUTHOR>
     * @date 2022/7/18 11:18
     */
    public static function chartDataByIncomeLine($adminId,$week)
    {
        $dates = array_map(function($date) {
            return Carbon::parse($date)->format('m/d');
        }, $week);
        $data = [];
        $list['name'] = '收益趋势';
        foreach ($dates as &$date) {
            $start = Carbon::parse($date)->startOfDay()->toDateTimeString();
            $end = Carbon::parse($date)->endOfDay()->toDateTimeString();
            $data[] = AgentIncome::where('admin_id',$adminId)->whereBetween('create_time',[$start,$end])->sum('amount');
        }
        $list['data'] = $data;
        return ['date' => $dates, 'list'=>$list];
    }

}