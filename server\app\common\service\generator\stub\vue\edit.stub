<template>
    <div class="edit-popup">
        <popup
            ref="popupRef"
            :title="popupTitle"
            :async="true"
            width="550px"
            @confirm="handleSubmit"
            @close="handleClose"
        >
            <el-form ref="formRef" :model="formData" label-width="90px" :rules="formRules">
{FORM_VIEW}
            </el-form>
        </popup>
    </div>
</template>

<script lang="ts" setup name="{SETUP_NAME}Edit">
import type { FormInstance } from 'element-plus'
import Popup from '@/components/popup/index.vue'
import {{IMPORT_LISTS} api{UPPER_CAMEL_NAME}Add, api{UPPER_CAMEL_NAME}Edit, api{UPPER_CAMEL_NAME}Detail } from '@/api/{API_DIR}'
import { timeFormat } from '@/utils/util'
import type { PropType } from 'vue'
defineProps({
    dictData: {
        type: Object as PropType<Record<string, any[]>>,
        default: () => ({})
    }
})
const emit = defineEmits(['success', 'close'])
const formRef = shallowRef<FormInstance>()
const popupRef = shallowRef<InstanceType<typeof Popup>>()
const mode = ref('add')
{TREE_CONST}

// 弹窗标题
const popupTitle = computed(() => {
    return mode.value == 'edit' ? '编辑{TABLE_COMMENT}' : '新增{TABLE_COMMENT}'
})

// 表单数据
const formData = reactive({
    {PK}: '',
{FORM_DATA}
})


// 表单验证
const formRules = reactive<any>({
{FORM_VALIDATE}
})


// 获取详情
const setFormData = async (data: Record<any, any>) => {
    for (const key in formData) {
        if (data[key] != null && data[key] != undefined) {
            //@ts-ignore
            formData[key] = data[key]
        }
    }
{CHECKBOX_SPLIT}
{FORM_DATE}
}

const getDetail = async (row: Record<string, any>) => {
    const data = await api{UPPER_CAMEL_NAME}Detail({
        {PK}: row.{PK}
    })
    setFormData(data)
}


// 提交按钮
const handleSubmit = async () => {
    await formRef.value?.validate()
    const data = { ...formData, {CHECKBOX_JOIN} }
    mode.value == 'edit' 
        ? await api{UPPER_CAMEL_NAME}Edit(data) 
        : await api{UPPER_CAMEL_NAME}Add(data)
    popupRef.value?.close()
    emit('success')
}

//打开弹窗
const open = (type = 'add') => {
    mode.value = type
    popupRef.value?.open()
}

// 关闭回调
const handleClose = () => {
    emit('close')
}

{GET_TREE_LISTS}

defineExpose({
    open,
    setFormData,
    getDetail
})
</script>
