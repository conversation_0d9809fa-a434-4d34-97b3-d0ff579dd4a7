<?php

namespace app\common\command;

use app\common\model\auth\Admin;
use app\common\model\income\AgentIncome;
use app\common\model\recharges\Recharges;
use app\common\model\user\User;
use app\common\service\ConfigService;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Log;

class AutoTaskForRecharges extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('recharges:task')
            ->setDescription('每分钟推广用户添加随机条数的充值记录');
    }

    protected function execute(Input $input, Output $output)
    {
        // 记录任务开始时间
        $startTime = microtime(true);
        $output->writeln('[' . date('Y-m-d H:i:s') . '] 开始执行推广用户添加随机条数的充值记录任务...');

        // 定义充值金额数组
        $rechargesAmountArr = [100, 300, 360, 600, 1200, 2400, 5000, 12000, 24000];

        // 获取后台数据中心配置信息
        $getAdminConfig = ConfigService::get('agent_config', 'agent_config', '');

        try {
            // 从数据库获取随机取N条男性用户数据
            // 获取所有男性用户ID
            $ids = User::where('sex',1)->column('id');

            if (count($ids) <= $getAdminConfig['rechargeUserNums']) {
                // 如果用户数量不足数据中心配置的配置数量，则取全部
                $userList = User::where('sex',1)->select()->toArray();

            }else {
                // 随机打乱并取数据中心配置的配置数量
                shuffle($ids);
                $randomIds = array_slice($ids, 0, $getAdminConfig['rechargeUserNums']);
                $userList = User::where('sex',1)->where('id', 'in', $randomIds)->select()->toArray();
            }

            //初始化推广员收益数组
            $agentIncome = [];
            //初始化用户累计充值余额数组
            $userMoney = [];
            //充值收益比例
            $rechargePercent = bcdiv($getAdminConfig['rechargePercent'], 100, 2);
            //实际金额与金币换算比例
            $convertPercent = $getAdminConfig['convertPercent'];
            foreach ($userList as $user) {
                // 为用户生成随机条数、随机金额的充值记录
                // 随机条数
                $rechargeCount = mt_rand($getAdminConfig['minRechargeNums'], $getAdminConfig['maxRechargeNums']);

                $recharges = [];
                $incomes = [];
                for ($j = 0; $j < $rechargeCount; $j++) {
                    // 随机金额
                    $rechargesAmount = $rechargesAmountArr[array_rand($rechargesAmountArr)];
                    // 转换金币
                    $rechargesCoin =  $rechargesAmount * $convertPercent;
                    $recharges[] = [
                        'user_id' => $user['id'],
                        'amount' => $rechargesCoin,
                        'create_time' => time(),
                    ];

                    //用户累计充值余额数组
                    if (isset($userMoney[$user['id']])) {
                        $userMoney[$user['id']] += $rechargesAmount;
                    }else{
                        $userMoney[$user['id']] = $rechargesAmount;
                    }

                    //推广员返佣记录数组
                    $incomes[] = [
                        'admin_id' => $user['admin_id'],            //推广员ID
                        'user_id' => $user['id'],                   //推广用户ID
                        'type' => 1,                                 //来自渠道类型  1-充值 2-消费
                        'amount' => bcmul($rechargesCoin,$rechargePercent,2),            //收益金额
                        'create_time' => date('Y-m-d H:i:s',time()),
                    ];

                    //推广员累计返佣金额数组
                    if (isset($agentIncome[$user['admin_id']])) {
                        $agentIncome[$user['admin_id']] += bcmul($rechargesCoin,$rechargePercent,2);
                    }else{
                        $agentIncome[$user['admin_id']] = bcmul($rechargesCoin,$rechargePercent,2);
                    }
                }

                $rechargeModel = new Recharges();
                $rechargeModel->saveAll($recharges);

                $incomeModel = new AgentIncome();
                $incomeModel->saveAll($incomes);

            }


            foreach ($userMoney as $userId => $amount) {
                $user = User::find($userId);
                $user->balance = $user['balance'] + $amount;
                $user->total_balance = $user['total_balance'] + $amount;
                $user->save();
            }

            foreach ($agentIncome as $adminId => $amount) {
                $admin = Admin::find($adminId);
                $admin->total_coin = $admin['total_coin'] + $amount;
                $admin->save();
            }

            // 记录执行结果
            $execTime = round(microtime(true) - $startTime, 3);
            $output->writeln("任务完成! 耗时: {$execTime}秒");

            // 写入日志
            Log::info("任务成功耗时 {$execTime}秒");

            return 0;

        }catch (\Exception $e){
            $output->writeln('任务执行出错: ' . $e->getMessage());
            Log::error('任务错误: ' . $e->getMessage());
            return 1;
        }
    }
}
