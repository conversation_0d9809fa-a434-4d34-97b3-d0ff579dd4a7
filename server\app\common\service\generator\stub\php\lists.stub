<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

{NAMESPACE}


{USE}
use app\common\lists\ListsSearchInterface;


/**
 * {CLASS_COMMENT}
 * Class {UPPER_CAMEL_NAME}Lists
 * @package app\{MODULE_NAME}\lists{PACKAGE_NAME}
 */
class {UPPER_CAMEL_NAME}Lists extends {EXTENDS_LISTS} implements ListsSearchInterface
{


    /**
     * @notes 设置搜索条件
     * @return \string[][]
     * <AUTHOR>
     * @date {DATE}
     */
    public function setSearch(): array
    {
        return [
{QUERY_CONDITION}
        ];
    }


    /**
     * @notes 获取{NOTES}列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date {DATE}
     */
    public function lists(): array
    {
        return {UPPER_CAMEL_NAME}::where($this->searchWhere)
            ->field([{FIELD_DATA}])
            ->limit($this->limitOffset, $this->limitLength)
            ->order(['{PK}' => 'desc'])
            ->select()
            ->toArray();
    }


    /**
     * @notes 获取{NOTES}数量
     * @return int
     * <AUTHOR>
     * @date {DATE}
     */
    public function count(): int
    {
        return {UPPER_CAMEL_NAME}::where($this->searchWhere)->count();
    }

}